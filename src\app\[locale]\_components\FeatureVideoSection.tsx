"use client";

import { useState, useRef } from "react";
import { Play, Pause } from "lucide-react";
import { cn } from "@/lib/utils";

type Props = {
  className?: string;
};

const FeatureVideoSection = ({ className }: Props) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [showThumbnail, setShowThumbnail] = useState(true);
  const videoRef = useRef<HTMLVideoElement>(null);

  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
        setIsPlaying(false);
      } else {
        videoRef.current.play();
        setIsPlaying(true);
        setShowThumbnail(false);
      }
    }
  };

  const handleVideoEnded = () => {
    setIsPlaying(false);
    setShowThumbnail(true);
  };

  return (
    <section
      className={cn(
        "flex min-h-[60vh] items-center justify-center py-16",
        className,
      )}
    >
      <div className="relative w-full max-w-4xl mx-auto">
        {/* Video Element */}
        <video
          ref={videoRef}
          className={cn(
            "w-full h-auto rounded-2xl shadow-2xl transition-opacity duration-300",
            showThumbnail ? "opacity-0" : "opacity-100"
          )}
          onEnded={handleVideoEnded}
          controls={isPlaying}
        >
          <source src="/assets/vids/feature-video.webm" type="video/webm" />
          Your browser does not support the video tag.
        </video>

        {/* Thumbnail with Decorative Elements */}
        {showThumbnail && (
          <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-300 dark:from-gray-700 dark:to-gray-900 rounded-2xl shadow-2xl overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-gradient-to-br from-gray-200/50 to-gray-400/50 dark:from-gray-600/50 dark:to-gray-800/50" />
            
            {/* Floating Decorative Elements */}
            {/* Chat Bubble */}
            <div className="absolute top-8 left-8 w-12 h-8 bg-blue-500 rounded-lg flex items-center justify-center shadow-lg transform rotate-12 hover:rotate-6 transition-transform duration-300">
              <div className="flex gap-1">
                <div className="w-1.5 h-1.5 bg-white rounded-full" />
                <div className="w-1.5 h-1.5 bg-white rounded-full" />
                <div className="w-1.5 h-1.5 bg-white rounded-full" />
              </div>
            </div>

            {/* Blue Line/Pen */}
            <div className="absolute top-12 right-16 w-32 h-2 bg-blue-600 rounded-full shadow-lg transform rotate-45 hover:rotate-12 transition-transform duration-300" />

            {/* Purple Triangle */}
            <div className="absolute bottom-20 left-16 w-0 h-0 border-l-8 border-r-8 border-b-12 border-l-transparent border-r-transparent border-b-purple-500 shadow-lg transform rotate-12 hover:rotate-45 transition-transform duration-300" />

            {/* Purple Circle */}
            <div className="absolute top-1/3 right-8 w-6 h-6 bg-purple-400 rounded-full shadow-lg hover:scale-110 transition-transform duration-300" />

            {/* Blue Cloud */}
            <div className="absolute top-16 right-8 transform hover:scale-110 transition-transform duration-300">
              <div className="relative">
                <div className="w-8 h-5 bg-blue-500 rounded-full" />
                <div className="absolute -top-2 left-2 w-6 h-6 bg-blue-500 rounded-full" />
                <div className="absolute -top-1 right-1 w-4 h-4 bg-blue-500 rounded-full" />
              </div>
            </div>

            {/* Additional Purple Shapes */}
            <div className="absolute bottom-32 right-24 w-4 h-8 bg-purple-400 rounded-full shadow-lg transform rotate-45 hover:rotate-12 transition-transform duration-300" />
            
            {/* Small Purple Circle */}
            <div className="absolute bottom-16 left-32 w-3 h-3 bg-purple-300 rounded-full shadow-lg hover:scale-125 transition-transform duration-300" />

            {/* Browser/Window Frame */}
            <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 w-80 max-w-[90%] h-12 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700">
              {/* Browser Controls */}
              <div className="flex items-center gap-2 px-3 py-2 border-b border-gray-200 dark:border-gray-700">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-red-400 rounded-full" />
                  <div className="w-2 h-2 bg-yellow-400 rounded-full" />
                  <div className="w-2 h-2 bg-green-400 rounded-full" />
                </div>
                {/* URL Bar */}
                <div className="flex-1 ml-2 px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs text-gray-600 dark:text-gray-300 text-center">
                  www.wacriativo.com
                </div>
                {/* Search Icon */}
                <div className="w-4 h-4 text-gray-400">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="11" cy="11" r="8" />
                    <path d="m21 21-4.35-4.35" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Play Button */}
            <button
              onClick={handlePlayPause}
              className="absolute inset-0 flex items-center justify-center group"
              aria-label="Play video"
            >
              <div className="w-20 h-20 bg-black/70 hover:bg-black/80 rounded-full flex items-center justify-center transition-all duration-300 group-hover:scale-110 shadow-2xl">
                <Play className="w-8 h-8 text-white ml-1" fill="white" />
              </div>
            </button>
          </div>
        )}

        {/* Play/Pause Button for Active Video */}
        {!showThumbnail && (
          <button
            onClick={handlePlayPause}
            className="absolute top-4 right-4 w-12 h-12 bg-black/70 hover:bg-black/80 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-lg"
            aria-label={isPlaying ? "Pause video" : "Play video"}
          >
            {isPlaying ? (
              <Pause className="w-5 h-5 text-white" fill="white" />
            ) : (
              <Play className="w-5 h-5 text-white ml-0.5" fill="white" />
            )}
          </button>
        )}
      </div>
    </section>
  );
};

export default FeatureVideoSection;
