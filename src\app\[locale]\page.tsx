import { LocaleType, routing } from "@/i18n/routing";
import { setRequestLocale } from "next-intl/server";
import HeroSection from "./_components/HeroSection";
import FeatureVideoSection from "./_components/FeatureVideoSection";

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}

const HomePage = async ({
  params,
}: {
  params: Promise<{ locale: LocaleType }>;
}) => {
  const { locale } = await params;

  // Enable static rendering
  setRequestLocale(locale);

  return (
    <main className="">
      <HeroSection className="container-full" />
      <FeatureVideoSection className="container" />
    </main>
  );
};

export default HomePage;
