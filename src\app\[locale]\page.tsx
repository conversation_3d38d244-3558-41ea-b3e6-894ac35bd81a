import { LocaleType, routing } from "@/i18n/routing";
import { setRequestLocale } from "next-intl/server";
import HeroSection from "./_components/HeroSection";

export function generateStaticParams() {
  return routing.locales.map((locale) => ({ locale }));
}

const HomePage = async ({
  params,
}: {
  params: Promise<{ locale: LocaleType }>;
}) => {
  const { locale } = await params;

  // Enable static rendering
  setRequestLocale(locale);

  return (
    <main className="">
      <HeroSection className="container-full" />
    </main>
  );
};

export default HomePage;
