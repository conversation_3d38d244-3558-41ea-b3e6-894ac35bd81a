import { getTranslations } from "next-intl/server";
import { But<PERSON> } from "@/components/ui/button";
import { Check } from "lucide-react";
import { cn } from "@/lib/utils";

type Props = {
  className?: string;
};

const HeroSection = async ({ className }: Props) => {
  const t = await getTranslations("hero");
  const tCommon = await getTranslations("common");

  return (
    <section
      className={cn(
        "flex min-h-[calc(100vh-5rem)] flex-col items-center justify-center gap-10 text-center text-balance",
        className,
      )}
    >
      <h1 className="max-w-3xl text-4xl leading-tight font-bold sm:text-5xl">
        {t("title")} <span className="text-primary">{t("titleHighlight")}</span>
      </h1>

      <p className="text-muted-foreground max-w-4xl text-lg sm:text-xl">
        {t("description")}
      </p>

      <div className="mb-8 grid grid-cols-[repeat(2,auto)] flex-wrap items-center justify-center gap-5">
        <Button size="lg" className="">
          {tCommon("request")}
        </Button>
        <Button variant="outline" size="lg">
          {tCommon("contact")}
        </Button>
        <span className="text-muted-foreground col-span-full flex items-center justify-center gap-2 text-sm">
          <Check className="text-premium h-4 w-4" />
          <span>{t("paymentInfo")}</span>
        </span>
      </div>
    </section>
  );
};

export default HeroSection;
